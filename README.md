# YouTube Subtitle Extractor

A professional full-stack application for extracting subtitles from YouTube videos with subscription-based pricing and Google authentication. Built with React, TypeScript, Vite, Express.js, Supabase, and Stripe.

## Features

- **Professional Subtitle Extraction**: Extract subtitles from any YouTube video
- **Multiple Languages**: Support for 70+ languages including auto-generated captions
- **3-Tier Pricing**: Starter, Pro, and Premium plans with different usage limits
- **Google Authentication**: Secure sign-in with Google OAuth via Supabase
- **Subscription Management**: Stripe-powered billing and subscription management
- **Usage Tracking**: Monitor monthly extraction limits and usage
- **Multiple Formats**: Download subtitles in VTT and TXT formats
- **Real-time Processing**: Live progress tracking during extraction
- **Responsive Design**: Clean, modern UI with Tailwind CSS
- **Full TypeScript**: Complete type safety throughout the application

## Tech Stack

**Frontend:**
- React 18 with TypeScript
- Vite for fast development
- Tailwind CSS for styling
- Radix UI components
- Framer Motion for animations
- React Hot Toast for notifications

**Backend:**
- Express.js with TypeScript (Local Development)
- Vercel Serverless Functions (Production)
- youtube-dl-exec for YouTube data extraction
- CORS enabled for cross-origin requests

**Authentication & Database:**
- Supabase for authentication and database
- Google OAuth integration
- PostgreSQL with Row Level Security

**Payment Processing:**
- Stripe for subscription management
- Webhook handling for real-time updates
- Secure payment processing

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Supabase account
- Stripe account
- Google Cloud Console project (for OAuth)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd YTSubtitleExtractor
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
```

Edit `.env` with your actual values:
- Supabase project URL and keys
- Stripe publishable and secret keys
- Stripe webhook secret
- Stripe price IDs for your products

4. Set up Supabase database:
- Create a new Supabase project
- Run the SQL in `database-setup.sql` in your Supabase SQL editor
- Configure Google OAuth in Supabase Auth settings

5. Set up Stripe:
- Create products and prices for Starter, Pro, and Premium plans
- Set up webhook endpoint pointing to `/api/stripe/webhook`
- Update price IDs in your environment variables

6. Start the development server:
```bash
npm run dev
```

This will start:
- Backend server on `http://localhost:3001`
- Frontend development server on `http://localhost:5173` (or next available port)

### Available Scripts

**Development:**
- `npm run dev` - Start both Express server and Vite frontend
- `npm run server` - Start only the Express backend server
- `npm run client` - Start only the Vite frontend development server

**Build & Deploy:**
- `npm run build` - Build both client and server for production
- `npm run build:client` - Build only the frontend
- `npm run build:server` - Build only the server
- `npm run preview` - Preview the production build

**Utilities:**
- `npm run health` - Check server health
- `npm run test:health` - Run local health checks
- `npm run clean` - Clean build artifacts
- `npm run validate:vercel` - Validate Vercel configuration

## Usage

1. **Sign Up**: Create an account using Google OAuth
2. **Choose a Plan**: Select from Starter ($9), Pro ($19), or Premium ($39) plans
3. **Extract Subtitles**:
   - Paste a YouTube video URL
   - Click "Get Available Languages"
   - Select your preferred language
   - Click "Extract Subtitles"
4. **Download**: Choose between VTT or TXT format and download
5. **Manage Subscription**: View usage, upgrade/downgrade plans, or cancel anytime

### Pricing Plans

- **Starter ($9/month)**: 50 videos/month, up to 60 minutes each
- **Pro ($19/month)**: 200 videos/month, up to 180 minutes each, batch processing
- **Premium ($39/month)**: Unlimited videos, unlimited length, API access

## API Endpoints

### Subtitle Extraction
- `GET /api/health` - Health check endpoint
- `GET /api/subtitles/languages/:videoId` - Get available subtitle languages for a video
- `GET /api/subtitles/download/:videoId/:langCode` - Download subtitles for a specific language

### Authentication & User Management
- `GET /api/auth/callback` - OAuth callback handler
- `GET /api/user/subscription?userId=:userId` - Get user subscription and usage
- `DELETE /api/user/subscription` - Cancel user subscription

### Payment Processing
- `POST /api/stripe/checkout` - Create Stripe checkout session
- `POST /api/stripe/webhook` - Handle Stripe webhooks

## Deployment

### Vercel Serverless Functions

This project is optimized for deployment on Vercel with serverless functions:

### Deployment Steps

1. **Connect to Vercel:**
   ```bash
   npm install -g vercel
   vercel login
   ```

2. **Set up environment variables in Vercel:**
   - Add all environment variables from `.env` to your Vercel project
   - Ensure webhook URLs point to your production domain

3. **Deploy:**
   ```bash
   vercel --prod
   ```

### Environment Variables

Required environment variables for production:

```bash
# Supabase
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Stripe
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Stripe Price IDs
STRIPE_STARTER_PRICE_ID=price_...
STRIPE_PRO_PRICE_ID=price_...
STRIPE_PREMIUM_PRICE_ID=price_...
```

3. **Environment Variables:**
   Set any required environment variables in the Netlify dashboard.

## Project Structure

```
├── src/                           # Frontend source code
│   ├── components/               # React components
│   │   ├── auth/                # Authentication components
│   │   ├── pricing/             # Pricing and subscription components
│   │   ├── dashboard/           # User dashboard
│   │   └── subscription/        # Subscription management
│   ├── contexts/                # React contexts (Auth)
│   ├── hooks/                   # Custom React hooks
│   ├── lib/                     # Utility functions and configurations
│   └── types/                   # TypeScript type definitions
├── api/                         # Vercel serverless functions
│   ├── auth/                    # Authentication endpoints
│   ├── stripe/                  # Stripe payment endpoints
│   ├── user/                    # User management endpoints
│   └── subtitles/              # Subtitle extraction endpoints
├── server/                      # Express server (local development)
├── public/                      # Static assets
├── database-setup.sql           # Supabase database schema
├── vercel.json                  # Vercel configuration
└── package.json                 # Dependencies and scripts
```

## Key Features

### Authentication System
- **Google OAuth**: Secure authentication via Supabase
- **User Profiles**: Automatic profile creation and management
- **Session Management**: Persistent login sessions

### Subscription Management
- **Stripe Integration**: Professional payment processing
- **3-Tier Pricing**: Flexible plans for different user needs
- **Usage Tracking**: Real-time monitoring of extraction limits
- **Subscription Controls**: Easy upgrade, downgrade, and cancellation

### Subtitle Extraction
- **YouTube Integration**: Direct video URL processing
- **Language Detection**: Automatic detection of available languages
- **Format Support**: VTT and TXT output formats
- **Progress Tracking**: Real-time extraction progress
- **Quality Processing**: Clean, formatted subtitle output

### User Experience
- **Responsive Design**: Works on all devices
- **Real-time Feedback**: Toast notifications and progress indicators
- **Intuitive Navigation**: Step-by-step extraction process
- **Dashboard**: Comprehensive user account management

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
